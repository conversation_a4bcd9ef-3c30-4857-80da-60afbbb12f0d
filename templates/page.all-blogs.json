{"sections": {"hero_section": {"type": "hero", "blocks": {"text_block": {"type": "text", "settings": {"text": "<h1>כל הבלוגים</h1><p>כל המאמרים והכתבות שלנו במקום אחד</p>", "width": "fit-content", "max_width": "narrow", "alignment": "center", "type_preset": "rte", "font": "var(--font-body--family)", "font_size": "1rem", "line_height": "normal", "letter_spacing": "normal", "case": "none", "wrap": "pretty", "color": "var(--color-foreground)", "background": false, "background_color": "#00000026", "corner_radius": 0, "padding-block-start": 48, "padding-block-end": 48, "padding-inline-start": 0, "padding-inline-end": 0, "custom_css_class": ""}, "blocks": {}}}, "block_order": ["text_block"], "custom_css": ["p {text-align: center;}", "h1 {text-align: center; font-size: 2.5rem; margin-bottom: 1rem;}", "{direction: rtl;}"], "settings": {"color_scheme": "", "section_width": "page-width", "height": "small", "height_mobile": "small", "flex_direction": "column", "flex_direction_mobile": "column", "justify_content": "center", "justify_content_mobile": "center", "align_items": "center", "align_items_mobile": "center", "gap": "medium", "gap_mobile": "medium", "background_type": "color", "background_color": "#f8f9fa", "background_gradient": "linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%)", "background_image_desktop": "", "background_image_mobile": "", "background_position": "center center", "background_size": "cover", "background_repeat": "no-repeat", "background_attachment": "scroll", "overlay": false, "overlay_color": "#000000", "overlay_opacity": 50, "overlay_style": "solid", "gradient_direction": "to top", "blurred_reflection": false, "reflection_opacity": 75, "padding-block-start": 60, "padding-block-end": 60}}, "button_filter": {"type": "button-filter", "blocks": {"all_blogs_button": {"type": "button", "settings": {"text": "כל הבלוגים", "link": "/pages/all-blogs"}}, "news_button": {"type": "button", "settings": {"text": "כל הכתבות", "link": "shopify://blogs/news"}}, "tech_button": {"type": "button", "settings": {"text": "פורמולות ליפוזומליות למצבים רפואיים ספציפיים", "link": "shopify://blogs/טכנולוגיה-ליפוזומלית"}}, "reviews_button": {"type": "button", "settings": {"text": "סקירות מדעיות של מוצרים ליפוזומליים", "link": "shopify://blogs/סקירות-מדעיות-של-מוצרים-ליפוזומליים"}}, "research_button": {"type": "button", "settings": {"text": "מאמרים מדעיים פרי מחקר של סקויה", "link": "shopify://blogs/מאמרים-מדעיים-פרי-מחקר-של-סקויה"}}, "liposomal_button": {"type": "button", "settings": {"text": "הכל על הטכנולוגיה הליפוזומלית", "link": "shopify://blogs/הכל-על-הטכנולוגיה-הליפוזומלית"}}}, "block_order": ["all_blogs_button", "news_button", "tech_button", "reviews_button", "research_button", "liposomal_button"], "custom_css": ["{direction: rtl;}", ".filter-button.active {background-color: #007bff; color: white;}"], "settings": {"color_scheme": "", "section_width": "page-width", "padding-block-start": 36, "padding-block-end": 0}}, "all_blogs_grid": {"type": "all-blogs", "settings": {"heading": "", "description": "", "articles_per_blog": 50, "columns_desktop": 3, "show_blog_name": true, "show_date": true, "show_excerpt": true, "excerpt_length": 200, "show_read_more": true, "read_more_text": "לקריאה", "image_height": 370, "show_load_more": false, "load_more_text": "טען עוד", "color_scheme": "", "padding-block-start": 40, "padding-block-end": 60}}}, "order": ["hero_section", "button_filter", "all_blogs_grid"]}