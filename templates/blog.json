/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "hero_paBfp6": {
      "type": "hero",
      "blocks": {
        "text_Wyir6m": {
          "type": "text",
          "settings": {
            "text": "",
            "width": "fit-content",
            "max_width": "narrow",
            "alignment": "left",
            "type_preset": "rte",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        },
        "text_cLMBnF": {
          "type": "text",
          "settings": {
            "text": "<p><br/>ידע, חדשנות ותובנות<br/></p>",
            "width": "fit-content",
            "max_width": "narrow",
            "alignment": "left",
            "type_preset": "custom",
            "font": "var(--font-body--family)",
            "font_size": "2rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 7,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        },
        "custom_liquid_f3Pmzc": {
          "type": "custom-liquid",
          "name": "t:names.custom_liquid",
          "settings": {
            "custom_liquid": "<div class=\"flex-1\">\n<p>כאן תוכלו למצוא תכנים מעמיקים על טכנולוגיית הליפוזום, חידושים מדעיים וכתבות מקצועיות על רכיבים ותוספי תזונה מתקדמים.זהו מרחב ידע שמאפשר לכם להיחשף למדע, למחקר ולתובנות שמניעים את סקויה קדימה.</p>\n<div style=\"text-align: right;\">\n<span class=\"blog-icon\"><img src=\"https://cdn.shopify.com/s/files/1/0714/5881/6158/files/Frame_1984077742_1.png?v=1750752289\" width=\"10\"></span>\n</div>\n</div>"
          },
          "blocks": {}
        }
      },
      "block_order": [
        "text_Wyir6m",
        "text_cLMBnF",
        "custom_liquid_f3Pmzc"
      ],
      "custom_css": [
        "p {text-align: right;}",
        "span.blog-icon img {width: 35px;}",
        "span.blog-icon {display: inline-block;}",
        "@media (min-width: 1024px) {.flex-1 {display: flex; align-items: center; gap: 15rem; flex-direction: row-reverse; }}",
        "h2,p,span,b {direction: rtl;}",
        ".custom-font-size p {font-weight: bold;}"
      ],
      "name": "t:names.hero",
      "settings": {
        "link": "",
        "open_in_new_tab": false,
        "media_type_1": "image",
        "image_1": "shopify://shop_images/articals_page_cover_2.jpg",
        "media_type_2": "image",
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-end",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 16,
        "section_width": "page-width",
        "section_height": "large",
        "section_height_custom": 50,
        "color_scheme": "scheme-6",
        "toggle_overlay": true,
        "overlay_color": "#00000026",
        "overlay_style": "gradient",
        "gradient_direction": "to top",
        "blurred_reflection": false,
        "reflection_opacity": 75,
        "padding-block-start": 40,
        "padding-block-end": 40
      }
    },
    "button_filter_L8HQwh": {
      "type": "button-filter",
      "blocks": {
        "button_all_blogs": {
          "type": "button",
          "settings": {
            "text": "כל הבלוגים",
            "link": "?show_all=true"
          }
        },
        "button_RXNnG6": {
          "type": "button",
          "settings": {
            "text": "כל הכתבות",
            "link": "shopify://blogs/news"
          }
        },
        "button_JUrUfJ": {
          "type": "button",
          "settings": {
            "text": "פורמולות ליפוזומליות למצבים רפואיים ספציפיים",
            "link": "shopify://blogs/טכנולוגיה-ליפוזומלית"
          }
        },
        "button_jEwtPW": {
          "type": "button",
          "settings": {
            "text": "סקירות מדעיות של מוצרים ליפוזומליים",
            "link": "shopify://blogs/סקירות-מדעיות-של-מוצרים-ליפוזומליים"
          }
        },
        "button_dfe8pg": {
          "type": "button",
          "settings": {
            "text": "מאמרים מדעיים פרי מחקר של סקויה",
            "link": "shopify://blogs/מאמרים-מדעיים-פרי-מחקר-של-סקויה"
          }
        },
        "button_n4gxqW": {
          "type": "button",
          "settings": {
            "text": "הכל על הטכנולוגיה הליפוזומלית",
            "link": "shopify://blogs/הכל-על-הטכנולוגיה-הליפוזומלית"
          }
        }
      },
      "block_order": [
        "button_all_blogs",
        "button_RXNnG6",
        "button_JUrUfJ",
        "button_jEwtPW",
        "button_dfe8pg",
        "button_n4gxqW"
      ],
      "custom_css": [
        "{direction: rtl;}"
      ],
      "name": "Button Filter",
      "settings": {
        "color_scheme": "",
        "section_width": "page-width",
        "padding-block-start": 36,
        "padding-block-end": 0
      }
    },
    "main": {
      "type": "main-blog",
      "blocks": {
        "title": {
          "type": "text",
          "name": "Title",
          "disabled": true,
          "settings": {
            "text": "<h1>{{ closest.blog.title }}</h1>",
            "width": "fit-content",
            "max_width": "narrow",
            "alignment": "left",
            "type_preset": "h2",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 48,
            "padding-block-end": 48,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        },
        "blog_post_d4Qm3g": {
          "type": "Blog-post",
          "name": "Custom Blogs Grid",
          "settings": {
            "blog": "{{ closest.blog }}",
            "heading": "",
            "description": "",
            "articles_to_show": 12,
            "columns_desktop": "3",
            "columns_mobile": "1",
            "text_alignment": "right",
            "grid_gap": 30,
            "image_height": 370,
            "card_padding": 16,
            "card_border_radius": 10,
            "show_card_shadow": true,
            "show_meta": true,
            "show_excerpt": true,
            "excerpt_length": 200,
            "show_read_more": true,
            "read_more_text": "לקריאה",
            "heading_color": "#000000",
            "text_color": "#666666",
            "card_background": "#ffffff",
            "card_title_color": "#000000",
            "meta_color": "#000000",
            "link_color": "#000000",
            "link_hover_color": "#000000",
            "heading_size": 32,
            "description_size": 16,
            "card_title_size": 24,
            "excerpt_size": 18,
            "section_padding_top": 16,
            "section_padding_bottom": 60
          },
          "blocks": {}
        },
        "static-blog-post-card": {
          "type": "_blog-post-card",
          "disabled": true,
          "static": true,
          "settings": {
            "alignment": "left"
          },
          "blocks": {
            "heading": {
              "type": "_heading",
              "name": "t:names.title",
              "static": true,
              "settings": {
                "type_preset": "h3",
                "font": "var(--font-primary--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "",
                "text": "",
                "read_only": true,
                "alignment": "left",
                "show_alignment": false,
                "padding-block-start": 0,
                "padding-block-end": 10,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "blog-post-details": {
              "type": "_blog-post-info-text",
              "name": "t:names.details",
              "static": true,
              "settings": {
                "show_date": false,
                "show_author": false,
                "type_preset": "h5",
                "alignment": "left",
                "show_alignment": false,
                "padding-block-start": 0,
                "padding-block-end": 0
              },
              "blocks": {}
            },
            "image": {
              "type": "_blog-post-image",
              "static": true,
              "settings": {
                "height": "large",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0
              },
              "blocks": {}
            }
          },
          "block_order": []
        }
      },
      "block_order": [
        "title",
        "blog_post_d4Qm3g"
      ],
      "custom_css": [
        "{direction: rtl;}",
        ".blog-posts-container {display: none;}"
      ],
      "settings": {
        "color_scheme": "",
        "padding-block-start": 0,
        "padding-block-end": 48
      }
    }
  },
  "order": [
    "hero_paBfp6",
    "button_filter_L8HQwh",
    "main"
  ]
}
